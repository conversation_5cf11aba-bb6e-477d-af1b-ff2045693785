name: CI

on:
  pull_request:
    paths-ignore:
      - "**.css"
      - "**.js"
      - "**.md"
      - "**.html"
      - "**.csv"
  schedule:
    # Run everday at midnight UTC / 5:30 IST
    - cron: "0 0 * * *"
env:
    HR_BRANCH: ${{ github.base_ref || github.ref_name }}

concurrency:
  group: develop-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  tests:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    env:
      NODE_ENV: "production"
      WITH_COVERAGE: ${{ github.event_name != 'pull_request' }}

    strategy:
      fail-fast: false

      matrix:
        container: [1, 2]

    name: Python Unit Tests

    services:
      mysql:
        image: mariadb:10.6
        env:
          MARIADB_ROOT_PASSWORD: 'root'
        ports:
          - 3306:3306
        options: --health-cmd="mariadb-admin ping" --health-interval=5s --health-timeout=2s --health-retries=3

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v2

      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'

      - name: Check for valid Python & Merge Conflicts
        run: |
          python -m compileall -f "${GITHUB_WORKSPACE}"
          if grep -lr --exclude-dir=node_modules "^<<<<<<< " "${GITHUB_WORKSPACE}"
              then echo "Found merge conflicts"
              exit 1
          fi

      - name: Setup Node
        uses: actions/setup-node@v2
        with:
          node-version: 18
          check-latest: true

      - name: Add to Hosts
        run: echo "127.0.0.1 test_site" | sudo tee -a /etc/hosts

      - name: Cache pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt', '**/pyproject.toml') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-

      - name: Cache node modules
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install
        run: |
          bash ${GITHUB_WORKSPACE}/.github/helper/install.sh
        env:
          FRAPPE_USER: ${{ github.event.inputs.user }}
          FRAPPE_BRANCH: ${{ github.event.inputs.branch }}

      - name: Run Tests
        run: cd ~/frappe-bench/ && bench --site test_site run-parallel-tests --app hrms --total-builds ${{ strategy.job-total }} --build-number ${{ matrix.container }}
        env:
          TYPE: server
          CAPTURE_COVERAGE: ${{ github.event_name != 'pull_request' }}

      - name: Upload coverage data
        uses: actions/upload-artifact@v4
        if: github.event_name != 'pull_request'
        with:
          name: coverage-${{ matrix.container }}
          path: /home/<USER>/frappe-bench/sites/coverage.xml

  coverage:
    name: Coverage Wrap Up
    needs: tests
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'pull_request' }}
    steps:
      - name: Clone
        uses: actions/checkout@v4

      - name: Download artifacts
        uses: actions/download-artifact@v4

      - name: Upload coverage data
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true
          verbose: true
