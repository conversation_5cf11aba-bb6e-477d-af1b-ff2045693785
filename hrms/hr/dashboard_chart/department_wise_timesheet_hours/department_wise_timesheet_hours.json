{"aggregate_function_based_on": "total_hours", "based_on": "", "chart_name": "Department wise Timesheet Hours", "chart_type": "Group By", "creation": "2022-08-21 17:32:09.625319", "docstatus": 0, "doctype": "Dashboard Chart", "document_type": "Timesheet", "dynamic_filters_json": "[[\"Timesheet\",\"company\",\"=\",\"frappe.defaults.get_user_default(\\\"Company\\\")\"]]", "filters_json": "[[\"Timesheet\",\"start_date\",\"Timespan\",\"this month\",false],[\"Timesheet\",\"docstatus\",\"=\",\"1\",false]]", "group_by_based_on": "department", "group_by_type": "Sum", "idx": 0, "is_public": 1, "is_standard": 1, "last_synced_on": "2022-08-21 17:56:03.184928", "modified": "2022-08-21 17:57:47.234034", "modified_by": "Administrator", "module": "HR", "name": "Department wise Timesheet Hours", "number_of_groups": 0, "owner": "Administrator", "parent_document_type": "", "roles": [], "source": "", "time_interval": "Yearly", "timeseries": 0, "timespan": "Last Year", "type": "Bar", "use_report_chart": 0, "value_based_on": "", "y_axis": []}