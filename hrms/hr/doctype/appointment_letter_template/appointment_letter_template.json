{"actions": [], "autoname": "field:template_name", "creation": "2019-12-26 12:20:14.219578", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["template_name", "introduction", "terms", "closing_notes"], "fields": [{"fieldname": "introduction", "fieldtype": "Long Text", "in_list_view": 1, "label": "Introduction", "reqd": 1}, {"fieldname": "closing_notes", "fieldtype": "Text", "label": "Closing Notes"}, {"fieldname": "terms", "fieldtype": "Table", "label": "Terms", "options": "Appointment Letter content", "reqd": 1}, {"fieldname": "template_name", "fieldtype": "Data", "label": "Template Name", "reqd": 1, "unique": 1}], "links": [], "modified": "2024-03-27 13:06:31.135009", "modified_by": "Administrator", "module": "HR", "name": "Appointment Letter Template", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}], "search_fields": "template_name", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "template_name", "track_changes": 1}