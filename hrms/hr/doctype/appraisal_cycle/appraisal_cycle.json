{"actions": [], "allow_rename": 1, "autoname": "field:cycle_name", "creation": "2022-08-24 15:05:29.694466", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["overview_tab", "cycle_name", "company", "status", "column_break_3", "start_date", "end_date", "section_break_4", "description", "settings_section", "column_break_vhzx", "kra_evaluation_method", "section_break_zykh", "calculate_final_score_based_on_formula", "final_score_formula", "applicable_for_tab", "filters_section", "branch", "department", "column_break_11", "designation", "employees_section", "get_employees", "appraisees"], "fields": [{"fieldname": "start_date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Start Date", "reqd": 1}, {"allow_in_quick_entry": 1, "fieldname": "end_date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "End Date", "reqd": 1}, {"collapsible": 1, "fieldname": "section_break_4", "fieldtype": "Section Break", "label": "Description"}, {"allow_in_quick_entry": 1, "fieldname": "description", "fieldtype": "Text Editor"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "applicable_for_tab", "fieldtype": "Tab Break", "label": "Applicable For"}, {"collapsible": 1, "collapsible_depends_on": "eval: doc.__islocal", "description": "Set optional filters to fetch employees in the appraisee list", "fieldname": "filters_section", "fieldtype": "Section Break", "label": "Filters"}, {"fieldname": "branch", "fieldtype": "Link", "label": "Branch", "options": "Branch"}, {"fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "designation", "fieldtype": "Link", "label": "Designation", "options": "Designation"}, {"fieldname": "get_employees", "fieldtype": "<PERSON><PERSON>", "label": "Get Employees"}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "employees_section", "fieldtype": "Section Break", "label": "Employees"}, {"fieldname": "appraisees", "fieldtype": "Table", "options": "Appraisee"}, {"fieldname": "cycle_name", "fieldtype": "Data", "in_list_view": 1, "label": "Cycle Name", "reqd": 1, "unique": 1}, {"fieldname": "overview_tab", "fieldtype": "Tab Break", "label": "Overview"}, {"fieldname": "settings_section", "fieldtype": "Section Break", "label": "Settings"}, {"default": "Automated Based on Goal Progress", "fieldname": "kra_evaluation_method", "fieldtype": "Select", "label": "KRA Evaluation Method", "options": "Automated Based on Goal Progress\nManual Rating"}, {"default": "Not Started", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Not Started\nIn Progress\nCompleted", "read_only": 1}, {"depends_on": "calculate_final_score_based_on_formula", "fieldname": "final_score_formula", "fieldtype": "Code", "label": "Final Score Formula", "mandatory_depends_on": "calculate_final_score_based_on_formula", "max_height": "5rem", "options": "PythonExpression"}, {"default": "0", "description": "By default, the Final Score is calculated as the average of Goal Score, Feedback Score, and Self Appraisal Score. Enable this to set a different formula", "fieldname": "calculate_final_score_based_on_formula", "fieldtype": "Check", "label": "Calculate Final Score based on Formula"}, {"fieldname": "column_break_vhzx", "fieldtype": "Column Break"}, {"fieldname": "section_break_zykh", "fieldtype": "Section Break", "hide_border": 1}], "index_web_pages_for_search": 1, "links": [{"link_doctype": "Appraisal", "link_fieldname": "appraisal_cycle"}, {"link_doctype": "Employee Performance Feedback", "link_fieldname": "appraisal_cycle"}, {"link_doctype": "Goal", "link_fieldname": "appraisal_cycle"}], "modified": "2024-05-29 18:15:06.443594", "modified_by": "Administrator", "module": "HR", "name": "Appraisal Cycle", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "select": 1, "share": 1}], "search_fields": "start_date, end_date", "sort_field": "creation", "sort_order": "DESC", "states": [{"color": "<PERSON>", "title": "Not Started"}, {"color": "Orange", "title": "In Progress"}, {"color": "Green", "title": "Completed"}], "track_changes": 1}