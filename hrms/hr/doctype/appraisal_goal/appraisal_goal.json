{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:27:44", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["kra", "section_break_2", "per_weightage", "column_break_4", "score", "section_break_6", "score_earned"], "fields": [{"description": "Key Responsibility Area", "fieldname": "kra", "fieldtype": "Small Text", "in_list_view": 1, "label": "Goal", "oldfieldname": "kra", "oldfieldtype": "Small Text", "print_width": "240px", "reqd": 1, "width": "240px"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "per_weightage", "fieldtype": "Float", "in_list_view": 1, "label": "Weightage (%)", "oldfieldname": "per_weightage", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "70px", "reqd": 1, "width": "70px"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "score", "fieldtype": "Float", "in_list_view": 1, "label": "Score (0-5)", "no_copy": 1, "oldfieldname": "score", "oldfieldtype": "Select", "print_width": "70px", "width": "70px"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "score_earned", "fieldtype": "Float", "in_list_view": 1, "label": "Score Earned", "no_copy": 1, "oldfieldname": "score_earned", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_width": "70px", "read_only": 1, "width": "70px"}], "idx": 1, "istable": 1, "links": [], "modified": "2024-03-27 13:06:31.782068", "modified_by": "Administrator", "module": "HR", "name": "Appraisal Goal", "owner": "Administrator", "permissions": [], "sort_field": "creation", "sort_order": "DESC", "states": []}