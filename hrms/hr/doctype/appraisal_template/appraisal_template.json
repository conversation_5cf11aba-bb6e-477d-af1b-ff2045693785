{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:template_title", "creation": "2012-07-03 13:30:39", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["template_title", "section_break_5", "description", "section_break_7", "goals", "rating_criteria"], "fields": [{"fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "width": "300px"}, {"fieldname": "goals", "fieldtype": "Table", "label": "KRAs", "oldfieldname": "kra_sheet", "oldfieldtype": "Table", "options": "Appraisal Template Goal", "reqd": 1}, {"collapsible": 1, "fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Description"}, {"fieldname": "section_break_7", "fieldtype": "Section Break"}, {"description": "Criteria based on which employee should be rated in Performance Feedback and Self Appraisal", "fieldname": "rating_criteria", "fieldtype": "Table", "label": "Rating Criteria", "options": "Employee Feedback Rating"}, {"fieldname": "template_title", "fieldtype": "Data", "in_list_view": 1, "label": "Appraisal Template Title", "oldfieldname": "kra_title", "oldfieldtype": "Data", "reqd": 1, "unique": 1}], "icon": "icon-file-text", "idx": 1, "links": [{"link_doctype": "Appraisal", "link_fieldname": "appraisal_template"}, {"link_doctype": "Designation", "link_fieldname": "appraisal_template"}], "modified": "2024-03-27 13:06:32.049388", "modified_by": "Administrator", "module": "HR", "name": "Appraisal Template", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"read": 1, "role": "Employee"}], "sort_field": "creation", "sort_order": "DESC", "states": []}