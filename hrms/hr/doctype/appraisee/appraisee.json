{"actions": [], "creation": "2022-08-24 21:07:51.412787", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["employee", "employee_name", "column_break_3", "appraisal_template", "department", "designation", "branch"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "in_list_view": 1, "label": "Employee", "options": "Employee", "reqd": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "in_list_view": 1, "label": "Employee Name", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "in_list_view": 1, "label": "Department", "options": "Department", "read_only": 1}, {"fetch_from": "employee.designation", "fieldname": "designation", "fieldtype": "Data", "in_list_view": 1, "label": "Designation", "read_only": 1}, {"fetch_from": "employee.branch", "fieldname": "branch", "fieldtype": "Link", "in_list_view": 1, "label": "Branch", "options": "Branch", "read_only": 1}, {"fieldname": "appraisal_template", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Appraisal Template", "options": "Appraisal Template"}], "istable": 1, "links": [], "modified": "2024-03-27 13:06:32.363365", "modified_by": "Administrator", "module": "HR", "name": "Appraisee", "owner": "Administrator", "permissions": [], "quick_entry": 1, "read_only": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}