{"actions": [], "allow_import": 1, "autoname": "naming_series:", "creation": "2013-01-10 16:34:13", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["attendance_details", "naming_series", "employee", "employee_name", "working_hours", "status", "leave_type", "leave_application", "column_break0", "attendance_date", "company", "department", "attendance_request", "half_day_status", "details_section", "shift", "in_time", "out_time", "column_break_18", "late_entry", "early_exit", "amended_from", "modify_half_day_status"], "fields": [{"fieldname": "attendance_details", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "Simple"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "HR-ATT-.YYYY.-", "reqd": 1, "set_only_once": 1}, {"fieldname": "employee", "fieldtype": "Link", "in_global_search": 1, "in_standard_filter": 1, "label": "Employee", "oldfieldname": "employee", "oldfieldtype": "Link", "options": "Employee", "reqd": 1, "search_index": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "in_global_search": 1, "label": "Employee Name", "oldfieldname": "employee_name", "oldfieldtype": "Data", "read_only": 1}, {"depends_on": "working_hours", "fieldname": "working_hours", "fieldtype": "Float", "label": "Working Hours", "precision": "1", "read_only": 1}, {"default": "Present", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nPresent\nAbsent\nOn Leave\nHalf Day\nWork From Home", "reqd": 1, "search_index": 1}, {"depends_on": "eval:in_list([\"On Leave\", \"Half Day\"], doc.status)", "fieldname": "leave_type", "fieldtype": "Link", "in_standard_filter": 1, "label": "Leave Type", "mandatory_depends_on": "eval:in_list([\"On Leave\", \"Half Day\"], doc.status)", "oldfieldname": "leave_type", "oldfieldtype": "Link", "options": "Leave Type"}, {"fieldname": "leave_application", "fieldtype": "Link", "label": "Leave Application", "no_copy": 1, "options": "Leave Application", "read_only": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "attendance_date", "fieldtype": "Date", "in_list_view": 1, "label": "Attendance Date", "oldfieldname": "attendance_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"fetch_from": "employee.company", "fieldname": "company", "fieldtype": "Link", "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "read_only": 1, "remember_last_selected_value": 1, "reqd": 1}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department", "read_only": 1}, {"fieldname": "shift", "fieldtype": "Link", "label": "Shift", "options": "Shift Type"}, {"fieldname": "attendance_request", "fieldtype": "Link", "label": "Attendance Request", "options": "Attendance Request", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "options": "Attendance", "print_hide": 1, "read_only": 1}, {"default": "0", "fieldname": "late_entry", "fieldtype": "Check", "label": "Late Entry"}, {"default": "0", "fieldname": "early_exit", "fieldtype": "Check", "label": "Early Exit"}, {"fieldname": "details_section", "fieldtype": "Section Break", "label": "Details"}, {"depends_on": "shift", "fieldname": "in_time", "fieldtype": "Datetime", "label": "In Time", "read_only": 1}, {"depends_on": "shift", "fieldname": "out_time", "fieldtype": "Datetime", "label": "Out Time", "read_only": 1}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.status==\"Half Day\";", "fieldname": "half_day_status", "fieldtype": "Select", "label": "Status for Other Half", "no_copy": 1, "options": "\nPresent\nAbsent"}, {"default": "0", "fieldname": "modify_half_day_status", "fieldtype": "Check", "hidden": 1, "label": "modify_half_day_status"}], "icon": "fa fa-ok", "idx": 1, "is_submittable": 1, "links": [], "modified": "2025-04-21 12:33:14.446458", "modified_by": "Administrator", "module": "HR", "name": "Attendance", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "submit": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "select": 1, "share": 1}], "row_format": "Dynamic", "search_fields": "employee,employee_name,attendance_date,status", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "employee_name", "track_changes": 1}