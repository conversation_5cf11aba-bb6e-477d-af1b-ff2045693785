{"actions": [], "allow_import": 1, "autoname": "HR-CMP-.YY.-.MM.-.#####", "creation": "2018-04-13 14:51:39.326768", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["employee", "employee_name", "department", "column_break_2", "leave_type", "leave_allocation", "worked_on", "work_from_date", "work_end_date", "half_day", "half_day_date", "column_break_4", "reason", "amended_from"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "in_list_view": 1, "label": "Employee", "options": "Employee", "reqd": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "label": "Employee Name", "read_only": 1}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department", "read_only": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "leave_type", "fieldtype": "Link", "label": "Leave Type", "options": "Leave Type"}, {"fieldname": "leave_allocation", "fieldtype": "Link", "label": "Leave Allocation", "options": "Leave Allocation", "read_only": 1}, {"fieldname": "worked_on", "fieldtype": "Section Break", "label": "Worked On Holiday"}, {"fieldname": "work_from_date", "fieldtype": "Date", "label": "Work From Date", "reqd": 1}, {"fieldname": "work_end_date", "fieldtype": "Date", "label": "Work End Date", "reqd": 1}, {"default": "0", "fieldname": "half_day", "fieldtype": "Check", "label": "Half Day"}, {"depends_on": "half_day", "fieldname": "half_day_date", "fieldtype": "Date", "label": "Half Day Date"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "reason", "fieldtype": "Small Text", "label": "Reason", "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Compensatory Leave Request", "print_hide": 1, "read_only": 1}], "is_submittable": 1, "links": [], "modified": "2024-03-27 13:06:45.747065", "modified_by": "Administrator", "module": "HR", "name": "Compensatory Leave Request", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "employee_name", "track_changes": 1}