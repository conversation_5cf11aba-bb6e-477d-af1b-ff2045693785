{"actions": [], "allow_import": 1, "autoname": "naming_series:", "creation": "2022-01-17 18:36:51.450395", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "employee", "employee_name", "column_break_4", "posting_date", "company", "department", "currency_section", "currency", "column_break_crso", "exchange_rate", "section_break_8", "purpose", "column_break_11", "advance_amount", "paid_amount", "pending_amount", "claimed_amount", "return_amount", "section_break_7", "column_break_18", "advance_account", "mode_of_payment", "column_break_nhlv", "repay_unclaimed_amount_from_salary", "more_info_section", "status", "column_break_kimx", "amended_from"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "HR-EAD-.YYYY.-"}, {"fieldname": "employee", "fieldtype": "Link", "in_list_view": 1, "label": "Employee", "options": "Employee", "reqd": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Read Only", "label": "Employee Name"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "reqd": 1}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department", "read_only": 1}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Purpose & Amount"}, {"fieldname": "purpose", "fieldtype": "Small Text", "in_list_view": 1, "label": "Purpose", "reqd": 1}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"description": "Amount of expense", "fieldname": "advance_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Advance Amount", "options": "currency", "reqd": 1}, {"description": "Amount that has been paid against this advance", "fieldname": "paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "no_copy": 1, "options": "currency", "read_only": 1}, {"description": "Amount claimed via Expense Claim", "fieldname": "claimed_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "no_copy": 1, "options": "currency", "read_only": 1}, {"fieldname": "section_break_7", "fieldtype": "Section Break", "label": "Accounting"}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "no_copy": 1, "options": "Draft\nPaid\nUnpaid\nClaimed\nReturned\nPartly Claimed and Returned\nCancelled", "read_only": 1}, {"fetch_from": "employee.company", "fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "read_only": 1, "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Employee Advance", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fetch_from": "company.default_employee_advance_account", "fieldname": "advance_account", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Advance Account", "options": "Account", "reqd": 1}, {"fieldname": "mode_of_payment", "fieldtype": "Link", "label": "Mode of Payment", "options": "Mode of Payment"}, {"allow_on_submit": 1, "description": "Amount scheduled for deduction via salary", "fieldname": "return_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Returned Amount", "no_copy": 1, "options": "currency", "read_only": 1}, {"default": "0", "fieldname": "repay_unclaimed_amount_from_salary", "fieldtype": "Check", "label": "Repay Unclaimed Amount from Salary"}, {"depends_on": "eval:cur_frm.doc.employee", "description": "Pending (unpaid) amount from previous advances", "fieldname": "pending_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Pending Amount", "no_copy": 1, "options": "currency", "read_only": 1}, {"depends_on": "eval:(doc.docstatus==1 || doc.employee)", "fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "reqd": 1}, {"depends_on": "currency", "fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate", "precision": "9", "print_hide": 1, "reqd": 1}, {"fieldname": "column_break_nhlv", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "more_info_section", "fieldtype": "Section Break", "label": "More Info"}, {"fieldname": "column_break_kimx", "fieldtype": "Column Break"}, {"collapsible": 1, "fieldname": "currency_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON><PERSON><PERSON> "}, {"fieldname": "column_break_crso", "fieldtype": "Column Break"}], "is_submittable": 1, "links": [], "modified": "2025-01-29 12:05:13.623633", "modified_by": "Administrator", "module": "HR", "name": "Employee Advance", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Expense Approver", "share": 1, "submit": 1, "write": 1}], "search_fields": "employee,employee_name", "sort_field": "creation", "sort_order": "DESC", "states": [{"color": "Red", "title": "Draft"}, {"color": "Green", "title": "Paid"}, {"color": "Orange", "title": "Unpaid"}, {"color": "Blue", "title": "Claimed"}, {"color": "<PERSON>", "title": "Returned"}, {"color": "Yellow", "title": "<PERSON>ly Claimed and Returned"}, {"color": "Red", "title": "Cancelled"}], "title_field": "employee_name", "track_changes": 1}