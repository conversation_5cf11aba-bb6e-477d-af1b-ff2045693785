{"actions": [], "allow_import": 1, "autoname": "EMP-CKIN-.MM.-.YYYY.-.######", "creation": "2019-06-10 11:56:34.536413", "doctype": "DocType", "engine": "InnoDB", "field_order": ["employee", "employee_name", "log_type", "shift", "column_break_4", "time", "device_id", "skip_auto_attendance", "attendance", "location_section", "latitude", "column_break_yqpi", "longitude", "section_break_ksbo", "fetch_geolocation", "geolocation", "shift_timings_section", "shift_start", "shift_end", "offshift", "column_break_vyyt", "shift_actual_start", "shift_actual_end"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "in_standard_filter": 1, "label": "Employee", "options": "Employee", "reqd": 1, "search_index": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "in_list_view": 1, "label": "Employee Name", "read_only": 1}, {"fieldname": "log_type", "fieldtype": "Select", "in_list_view": 1, "label": "Log Type", "options": "\nIN\nOUT"}, {"fieldname": "shift", "fieldtype": "Link", "label": "Shift", "options": "Shift Type", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"default": "Now", "fieldname": "time", "fieldtype": "Datetime", "in_list_view": 1, "label": "Time", "permlevel": 1, "read_only_depends_on": "eval:doc.attendance;", "reqd": 1}, {"fieldname": "device_id", "fieldtype": "Data", "label": "Location / Device ID"}, {"default": "0", "fieldname": "skip_auto_attendance", "fieldtype": "Check", "label": "Skip Auto Attendance"}, {"fieldname": "attendance", "fieldtype": "Link", "label": "Attendance Marked", "options": "Attendance", "read_only": 1}, {"fieldname": "shift_start", "fieldtype": "Datetime", "hidden": 1, "label": "Shift Start"}, {"fieldname": "shift_end", "fieldtype": "Datetime", "hidden": 1, "label": "Shift End"}, {"fieldname": "shift_actual_start", "fieldtype": "Datetime", "hidden": 1, "label": "Shift Actual Start"}, {"fieldname": "shift_actual_end", "fieldtype": "Datetime", "hidden": 1, "label": "Shift Actual End"}, {"fieldname": "location_section", "fieldtype": "Section Break", "label": "Location"}, {"fieldname": "geolocation", "fieldtype": "Geolocation", "label": "Geolocation", "read_only": 1}, {"fieldname": "shift_timings_section", "fieldtype": "Section Break", "label": "Shift Timings"}, {"fieldname": "column_break_vyyt", "fieldtype": "Column Break"}, {"fieldname": "latitude", "fieldtype": "Float", "label": "Latitude", "precision": "7", "read_only": 1}, {"fieldname": "longitude", "fieldtype": "Float", "label": "Longitude", "precision": "7", "read_only": 1}, {"fieldname": "column_break_yqpi", "fieldtype": "Column Break"}, {"fieldname": "section_break_ksbo", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "fetch_geolocation", "fieldtype": "<PERSON><PERSON>", "label": "Fetch Geolocation"}, {"default": "0", "fieldname": "offshift", "fieldtype": "Check", "hidden": 1, "label": "Off-shift", "read_only": 1}], "links": [], "modified": "2025-03-05 15:36:10.014354", "modified_by": "Administrator", "module": "HR", "name": "Employee Checkin", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "read": 1, "role": "Employee", "write": 1}, {"delete": 1, "email": 1, "export": 1, "permlevel": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"delete": 1, "email": 1, "export": 1, "permlevel": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"delete": 1, "email": 1, "export": 1, "permlevel": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Employee"}], "sort_field": "creation", "sort_order": "ASC", "states": [], "title_field": "employee_name", "track_changes": 1}