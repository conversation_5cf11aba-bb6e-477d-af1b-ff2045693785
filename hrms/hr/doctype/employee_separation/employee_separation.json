{"actions": [], "autoname": "HR-EMP-SEP-.YYYY.-.#####", "creation": "2018-05-10 02:29:16.740490", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["employee", "employee_name", "department", "designation", "employee_grade", "column_break_7", "company", "boarding_status", "resignation_letter_date", "boarding_begins_on", "project", "table_for_activity", "employee_separation_template", "activities", "notify_users_by_email", "section_break_14", "exit_interview", "amended_from"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "label": "Employee", "options": "Employee", "reqd": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "in_list_view": 1, "label": "Employee Name", "read_only": 1}, {"fetch_from": "employee.resignation_letter_date", "fieldname": "resignation_letter_date", "fieldtype": "Date", "in_list_view": 1, "label": "Resignation Letter Date", "read_only": 1}, {"allow_on_submit": 1, "default": "Pending", "fieldname": "boarding_status", "fieldtype": "Select", "label": "Status", "options": "Pending\nIn Process\nCompleted", "read_only": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "notify_users_by_email", "fieldtype": "Check", "label": "Notify users by email"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "employee_separation_template", "fieldtype": "Link", "label": "Employee Separation Template", "options": "Employee Separation Template"}, {"fetch_from": "employee.company", "fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project", "read_only": 1}, {"fetch_from": "employee.department", "fieldname": "department", "fieldtype": "Link", "in_list_view": 1, "label": "Department", "options": "Department", "read_only": 1}, {"fetch_from": "employee.designation", "fieldname": "designation", "fieldtype": "Link", "in_list_view": 1, "label": "Designation", "options": "Designation", "read_only": 1}, {"fetch_from": "employee.grade", "fieldname": "employee_grade", "fieldtype": "Link", "label": "Employee Grade", "options": "Employee Grade", "read_only": 1}, {"fieldname": "table_for_activity", "fieldtype": "Section Break", "label": "Separation Activities"}, {"allow_on_submit": 1, "fieldname": "activities", "fieldtype": "Table", "label": "Activities", "options": "Employee Boarding Activity"}, {"fieldname": "section_break_14", "fieldtype": "Section Break"}, {"fieldname": "exit_interview", "fieldtype": "Text Editor", "label": "Exit Interview Summary"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Employee Separation", "print_hide": 1, "read_only": 1}, {"fieldname": "boarding_begins_on", "fieldtype": "Date", "label": "Separation Begins On", "reqd": 1}], "is_submittable": 1, "links": [], "modified": "2024-03-27 13:09:41.099448", "modified_by": "Administrator", "module": "HR", "name": "Employee Separation", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "employee_name", "track_changes": 1}